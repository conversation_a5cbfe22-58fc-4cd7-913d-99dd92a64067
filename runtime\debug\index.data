a:59:{s:13:"686baac93034a";a:13:{s:3:"tag";s:13:"686baac93034a";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886535.957809;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8633824;s:14:"processingTime";d:2.527582883834839;}s:13:"686baacab76df";a:13:{s:3:"tag";s:13:"686baacab76df";s:3:"url";s:36:"http://silver/backend/position/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886538.552265;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10376200;s:14:"processingTime";d:0.24744200706481934;}s:13:"686baad11c07d";a:13:{s:3:"tag";s:13:"686baad11c07d";s:3:"url";s:42:"http://silver/backend/worker-finance/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886544.905143;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10446600;s:14:"processingTime";d:0.3063089847564697;}s:13:"686baad357662";a:13:{s:3:"tag";s:13:"686baad357662";s:3:"url";s:58:"http://silver/backend/worker-finance/export-monthly-report";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886547.107562;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9970760;s:14:"processingTime";d:0.2818021774291992;}s:13:"686baad894d3b";a:13:{s:3:"tag";s:13:"686baad894d3b";s:3:"url";s:58:"http://silver/backend/worker-finance/export-monthly-report";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886552.319202;s:10:"statusCode";i:200;s:8:"sqlCount";i:992;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:39620088;s:14:"processingTime";d:2.215535879135132;}s:13:"686baadaa5b6c";a:13:{s:3:"tag";s:13:"686baadaa5b6c";s:3:"url";s:76:"http://silver/backend/worker-finance/index?_pjax=%23worker_finance-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886553.318602;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10224592;s:14:"processingTime";d:1.4266338348388672;}s:13:"686baadadb931";a:13:{s:3:"tag";s:13:"686baadadb931";s:3:"url";s:42:"http://silver/backend/worker-finance/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886553.991331;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10447872;s:14:"processingTime";d:0.9818041324615479;}s:13:"686bac5669d4f";a:13:{s:3:"tag";s:13:"686bac5669d4f";s:3:"url";s:42:"http://silver/backend/worker-finance/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886934.141603;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10447872;s:14:"processingTime";d:0.3552680015563965;}s:13:"686bac5abba1f";a:13:{s:3:"tag";s:13:"686bac5abba1f";s:3:"url";s:58:"http://silver/backend/worker-finance/export-monthly-report";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886938.517458;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9971984;s:14:"processingTime";d:0.2863039970397949;}s:13:"686bac64a759d";a:13:{s:3:"tag";s:13:"686bac64a759d";s:3:"url";s:58:"http://silver/backend/worker-finance/export-monthly-report";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886948.46399;s:10:"statusCode";i:200;s:8:"sqlCount";i:495;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:37071344;s:14:"processingTime";d:1.2606911659240723;}s:13:"686bac65d543c";a:13:{s:3:"tag";s:13:"686bac65d543c";s:3:"url";s:76:"http://silver/backend/worker-finance/index?_pjax=%23worker_finance-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751886949.470128;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10224592;s:14:"processingTime";d:0.4712510108947754;}s:13:"686bacd936472";a:13:{s:3:"tag";s:13:"686bacd936472";s:3:"url";s:42:"http://silver/backend/worker-finance/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887064.972362;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10447872;s:14:"processingTime";d:0.31204795837402344;}s:13:"686bacdbd835b";a:13:{s:3:"tag";s:13:"686bacdbd835b";s:3:"url";s:58:"http://silver/backend/worker-finance/export-monthly-report";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887067.665023;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9970760;s:14:"processingTime";d:0.25180697441101074;}s:13:"686bacde459ad";a:13:{s:3:"tag";s:13:"686bacde459ad";s:3:"url";s:58:"http://silver/backend/worker-finance/export-monthly-report";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887070.026219;s:10:"statusCode";i:200;s:8:"sqlCount";i:495;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:37070744;s:14:"processingTime";d:1.2887890338897705;}s:13:"686bacdf723df";a:13:{s:3:"tag";s:13:"686bacdf723df";s:3:"url";s:76:"http://silver/backend/worker-finance/index?_pjax=%23worker_finance-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887071.025506;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10223368;s:14:"processingTime";d:0.5079870223999023;}s:13:"686bad757d07f";a:13:{s:3:"tag";s:13:"686bad757d07f";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887221.272485;s:10:"statusCode";i:200;s:8:"sqlCount";i:423;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:16737088;s:14:"processingTime";d:0.9615540504455566;}s:13:"686bad78d0e5f";a:13:{s:3:"tag";s:13:"686bad78d0e5f";s:3:"url";s:36:"http://silver/backend/invoice/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887224.516591;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11108072;s:14:"processingTime";d:0.4148118495941162;}s:13:"686bad79c8a09";a:13:{s:3:"tag";s:13:"686bad79c8a09";s:3:"url";s:98:"http://silver/backend/invoice/get-products-block-sizes?ids=7%2C9%2C8%2C11%2C10%2C13%2C12%2C14%2C15";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887225.51559;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9271760;s:14:"processingTime";d:0.32838010787963867;}s:13:"686bad7b29c70";a:13:{s:3:"tag";s:13:"686bad7b29c70";s:3:"url";s:55:"http://silver/backend/invoice/get-drivers?client_id=374";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887226.818551;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9230448;s:14:"processingTime";d:0.37096691131591797;}s:13:"686bad7b52e64";a:13:{s:3:"tag";s:13:"686bad7b52e64";s:3:"url";s:54:"http://silver/backend/invoice/get-prices?client_id=374";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887226.819546;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9363360;s:14:"processingTime";d:0.5582540035247803;}s:13:"686bad823782f";a:13:{s:3:"tag";s:13:"686bad823782f";s:3:"url";s:36:"http://silver/backend/invoice/create";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887234.004749;s:10:"statusCode";i:200;s:8:"sqlCount";i:47;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10378224;s:14:"processingTime";d:0.3998382091522217;}s:13:"686bad82a44ee";a:13:{s:3:"tag";s:13:"686bad82a44ee";s:3:"url";s:62:"http://silver/backend/invoice/index?_pjax=%23invoice-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887234.456349;s:10:"statusCode";i:200;s:8:"sqlCount";i:424;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:15423992;s:14:"processingTime";d:1.0033931732177734;}s:13:"686bad83a9e3e";a:13:{s:3:"tag";s:13:"686bad83a9e3e";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887235.113563;s:10:"statusCode";i:200;s:8:"sqlCount";i:424;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:16743072;s:14:"processingTime";d:1.3671610355377197;}s:13:"686bad8aa9d5e";a:13:{s:3:"tag";s:13:"686bad8aa9d5e";s:3:"url";s:41:"http://silver/backend/invoice/view?id=899";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887242.445297;s:10:"statusCode";i:200;s:8:"sqlCount";i:19;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10288376;s:14:"processingTime";d:0.3262629508972168;}s:13:"686bad8c0cd2d";a:13:{s:3:"tag";s:13:"686bad8c0cd2d";s:3:"url";s:47:"http://silver/backend/print-invoice/view?id=899";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887243.85133;s:10:"statusCode";i:200;s:8:"sqlCount";i:18;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9932680;s:14:"processingTime";d:0.27517199516296387;}s:13:"686bad927cb28";a:13:{s:3:"tag";s:13:"686bad927cb28";s:3:"url";s:53:"http://silver/backend/print-invoice/save-print-number";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887250.283228;s:10:"statusCode";i:200;s:8:"sqlCount";i:9;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8985168;s:14:"processingTime";d:0.260958194732666;}s:13:"686bad92b1905";a:13:{s:3:"tag";s:13:"686bad92b1905";s:3:"url";s:53:"http://silver/backend/print-invoice/save-print-number";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887250.413823;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8981624;s:14:"processingTime";d:0.32579517364501953;}s:13:"686bad9868656";a:13:{s:3:"tag";s:13:"686bad9868656";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887256.220171;s:10:"statusCode";i:200;s:8:"sqlCount";i:424;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:16743696;s:14:"processingTime";d:0.8642888069152832;}s:13:"686baec783e27";a:13:{s:3:"tag";s:13:"686baec783e27";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887559.303567;s:10:"statusCode";i:200;s:8:"sqlCount";i:424;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:16743072;s:14:"processingTime";d:0.7450389862060547;}s:13:"686baec9c749c";a:13:{s:3:"tag";s:13:"686baec9c749c";s:3:"url";s:36:"http://silver/backend/invoice/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887561.589506;s:10:"statusCode";i:200;s:8:"sqlCount";i:15;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11108072;s:14:"processingTime";d:0.30493783950805664;}s:13:"686baecaa1653";a:13:{s:3:"tag";s:13:"686baecaa1653";s:3:"url";s:98:"http://silver/backend/invoice/get-products-block-sizes?ids=7%2C9%2C8%2C11%2C10%2C13%2C12%2C14%2C15";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887562.477228;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9271760;s:14:"processingTime";d:0.22135496139526367;}s:13:"686baecc606be";a:13:{s:3:"tag";s:13:"686baecc606be";s:3:"url";s:55:"http://silver/backend/invoice/get-drivers?client_id=370";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887564.211253;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9237952;s:14:"processingTime";d:0.20495200157165527;}s:13:"686baecc842e7";a:13:{s:3:"tag";s:13:"686baecc842e7";s:3:"url";s:54:"http://silver/backend/invoice/get-prices?client_id=370";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887564.212253;s:10:"statusCode";i:200;s:8:"sqlCount";i:14;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9364112;s:14:"processingTime";d:0.36902618408203125;}s:13:"686baed3bdf4a";a:13:{s:3:"tag";s:13:"686baed3bdf4a";s:3:"url";s:36:"http://silver/backend/invoice/create";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887571.597722;s:10:"statusCode";i:200;s:8:"sqlCount";i:44;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10371128;s:14:"processingTime";d:0.36014795303344727;}s:13:"686baed432296";a:13:{s:3:"tag";s:13:"686baed432296";s:3:"url";s:62:"http://silver/backend/invoice/index?_pjax=%23invoice-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887572.008775;s:10:"statusCode";i:200;s:8:"sqlCount";i:425;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:15442352;s:14:"processingTime";d:0.9285111427307129;}s:13:"686baed510932";a:13:{s:3:"tag";s:13:"686baed510932";s:3:"url";s:35:"http://silver/backend/invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751887572.669241;s:10:"statusCode";i:200;s:8:"sqlCount";i:425;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:16765528;s:14:"processingTime";d:1.1508781909942627;}s:13:"686bb0f1a5245";a:13:{s:3:"tag";s:13:"686bb0f1a5245";s:3:"url";s:20:"http://silver/logout";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888113.378249;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8609984;s:14:"processingTime";d:0.3194308280944824;}s:13:"686bb0f1d4755";a:13:{s:3:"tag";s:13:"686bb0f1d4755";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888113.742992;s:10:"statusCode";i:200;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7868304;s:14:"processingTime";d:0.18599700927734375;}s:13:"686bb0fc9100b";a:13:{s:3:"tag";s:13:"686bb0fc9100b";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888124.472975;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8633704;s:14:"processingTime";d:1.6160030364990234;}s:13:"686bb0fe1969f";a:13:{s:3:"tag";s:13:"686bb0fe1969f";s:3:"url";s:19:"http://silver/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888124.68625;s:10:"statusCode";i:302;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8633808;s:14:"processingTime";d:2.720005989074707;}s:13:"686bb0ffafbd8";a:13:{s:3:"tag";s:13:"686bb0ffafbd8";s:3:"url";s:36:"http://silver/backend/position/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888127.48309;s:10:"statusCode";i:302;s:8:"sqlCount";i:7;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8866568;s:14:"processingTime";d:0.24052095413208008;}s:13:"686bb1000d104";a:13:{s:3:"tag";s:13:"686bb1000d104";s:3:"url";s:41:"http://silver/backend/sales-invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888127.774991;s:10:"statusCode";i:200;s:8:"sqlCount";i:433;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:17478840;s:14:"processingTime";d:0.9548211097717285;}s:13:"686bb1066cbd5";a:13:{s:3:"tag";s:13:"686bb1066cbd5";s:3:"url";s:42:"http://silver/backend/sales-invoice/create";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888134.186194;s:10:"statusCode";i:200;s:8:"sqlCount";i:17;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11116704;s:14:"processingTime";d:0.33690595626831055;}s:13:"686bb107611e0";a:13:{s:3:"tag";s:13:"686bb107611e0";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888135.111401;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9162728;s:14:"processingTime";d:0.30227208137512207;}s:13:"686bb107875ac";a:13:{s:3:"tag";s:13:"686bb107875ac";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888135.114541;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9162728;s:14:"processingTime";d:0.4391520023345947;}s:13:"686bb107ba9f5";a:13:{s:3:"tag";s:13:"686bb107ba9f5";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888135.120198;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9162728;s:14:"processingTime";d:0.6320328712463379;}s:13:"686bb107e9d95";a:13:{s:3:"tag";s:13:"686bb107e9d95";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888135.119461;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9162728;s:14:"processingTime";d:0.8158321380615234;}s:13:"686bb10823ada";a:13:{s:3:"tag";s:13:"686bb10823ada";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888135.121222;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9162728;s:14:"processingTime";d:1.0193548202514648;}s:13:"686bb1085263d";a:13:{s:3:"tag";s:13:"686bb1085263d";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888135.124217;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9162728;s:14:"processingTime";d:1.1724169254302979;}s:13:"686bb10878b5b";a:13:{s:3:"tag";s:13:"686bb10878b5b";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888135.450501;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9162728;s:14:"processingTime";d:1.0583291053771973;}s:13:"686bb1089bd41";a:13:{s:3:"tag";s:13:"686bb1089bd41";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888135.621363;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9162728;s:14:"processingTime";d:1.0361340045928955;}s:13:"686bb108e1ee7";a:13:{s:3:"tag";s:13:"686bb108e1ee7";s:3:"url";s:56:"http://silver/backend/sales-invoice/get-batch-quantities";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888135.85189;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9162728;s:14:"processingTime";d:1.1113319396972656;}s:13:"686bb10a462f6";a:13:{s:3:"tag";s:13:"686bb10a462f6";s:3:"url";s:61:"http://silver/backend/sales-invoice/get-drivers?client_id=370";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888138.07281;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9155552;s:14:"processingTime";d:0.2415001392364502;}s:13:"686bb10a6cb43";a:13:{s:3:"tag";s:13:"686bb10a6cb43";s:3:"url";s:60:"http://silver/backend/sales-invoice/get-prices?client_id=370";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888138.073813;s:10:"statusCode";i:200;s:8:"sqlCount";i:16;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9339296;s:14:"processingTime";d:0.4276449680328369;}s:13:"686bb114300b0";a:13:{s:3:"tag";s:13:"686bb114300b0";s:3:"url";s:60:"http://silver/backend/sales-invoice/get-prices?client_id=378";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888147.978806;s:10:"statusCode";i:200;s:8:"sqlCount";i:16;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9337856;s:14:"processingTime";d:0.27350306510925293;}s:13:"686bb11464564";a:13:{s:3:"tag";s:13:"686bb11464564";s:3:"url";s:61:"http://silver/backend/sales-invoice/get-drivers?client_id=378";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888147.977813;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9149272;s:14:"processingTime";d:0.4730229377746582;}s:13:"686bb11813c90";a:13:{s:3:"tag";s:13:"686bb11813c90";s:3:"url";s:42:"http://silver/backend/sales-invoice/create";s:4:"ajax";i:1;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888151.882113;s:10:"statusCode";i:200;s:8:"sqlCount";i:43;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:10195904;s:14:"processingTime";d:0.3772008419036865;}s:13:"686bb11885c60";a:13:{s:3:"tag";s:13:"686bb11885c60";s:3:"url";s:68:"http://silver/backend/sales-invoice/index?_pjax=%23invoice-grid-pjax";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888152.310215;s:10:"statusCode";i:200;s:8:"sqlCount";i:430;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:16115960;s:14:"processingTime";d:0.9600908756256104;}s:13:"686bb1196858b";a:13:{s:3:"tag";s:13:"686bb1196858b";s:3:"url";s:41:"http://silver/backend/sales-invoice/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:1751888152.969463;s:10:"statusCode";i:200;s:8:"sqlCount";i:434;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:17497320;s:14:"processingTime";d:1.1993579864501953;}}